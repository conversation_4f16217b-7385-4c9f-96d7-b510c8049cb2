#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
query_view_dict_prj_yisi_chaifen.py - 查询视图v_dict_prj_yisi_chaifen的详细功能演示
展示项目编码列表拆分后的各种查询应用
"""

import pymysql
import pandas as pd
from config import get_db_config

def show_view_overview():
    """显示视图概览信息"""
    try:
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        print("=== 视图 v_dict_prj_yisi_chaifen 概览 ===")
        
        # 基本统计
        cursor.execute("""
            SELECT 
                COUNT(*) as 拆分后总记录数,
                COUNT(DISTINCT 原记录ID) as 原表记录数,
                COUNT(DISTINCT 客户名称) as 涉及客户数,
                COUNT(DISTINCT 项目编码) as 唯一项目编码数
            FROM v_dict_prj_yisi_chaifen
        """)
        
        stats = cursor.fetchone()
        print(f"拆分后总记录数: {stats[0]}")
        print(f"原表记录数: {stats[1]}")
        print(f"涉及客户数: {stats[2]}")
        print(f"唯一项目编码数: {stats[3]}")
        
        # 拆分效果统计
        cursor.execute("""
            SELECT 
                原记录项目总数,
                COUNT(*) as 记录数,
                COUNT(*) * 原记录项目总数 as 拆分后记录数
            FROM v_dict_prj_yisi_chaifen
            GROUP BY 原记录项目总数
            ORDER BY 原记录项目总数
        """)
        
        print("\n项目数量分布:")
        results = cursor.fetchall()
        for row in results:
            print(f"  {row[0]}个项目的记录: {row[1]}条原记录 -> {row[2]}条拆分记录")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"[错误] 查询视图概览失败: {e}")

def show_detailed_records():
    """显示详细的拆分记录"""
    try:
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        
        print("\n=== 详细拆分记录展示 ===")
        
        query = """
        SELECT 
            原记录ID,
            客户名称,
            项目编码,
            项目名称,
            项目序号,
            原记录项目总数,
            IT收入累加值,
            风险等级,
            时间窗口开始,
            时间窗口结束
        FROM v_dict_prj_yisi_chaifen 
        ORDER BY 原记录ID, 项目序号
        """
        
        df = pd.read_sql(query, conn)
        
        # 按原记录ID分组显示
        for record_id in df['原记录ID'].unique():
            record_data = df[df['原记录ID'] == record_id]
            first_row = record_data.iloc[0]
            
            print(f"\n--- 原记录ID: {record_id} ---")
            print(f"客户名称: {first_row['客户名称']}")
            print(f"时间窗口: {first_row['时间窗口开始']} 到 {first_row['时间窗口结束']}")
            print(f"IT收入累加值: {first_row['IT收入累加值']} 万元")
            print(f"风险等级: {first_row['风险等级']}")
            print(f"项目总数: {first_row['原记录项目总数']}")
            print("拆分后的项目:")
            
            for _, row in record_data.iterrows():
                print(f"  {row['项目序号']}. {row['项目编码']} - {row['项目名称'][:50]}...")
        
        conn.close()
        
    except Exception as e:
        print(f"[错误] 显示详细记录失败: {e}")

def show_customer_analysis():
    """按客户分析项目分布"""
    try:
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        
        print("\n=== 客户项目分布分析 ===")
        
        query = """
        SELECT 
            客户名称,
            COUNT(*) as 项目总数,
            COUNT(DISTINCT 原记录ID) as 疑似拆分记录数,
            AVG(IT收入累加值) as 平均IT收入,
            MAX(IT收入累加值) as 最大IT收入,
            GROUP_CONCAT(DISTINCT 风险等级) as 风险等级分布,
            MIN(时间窗口开始) as 最早时间,
            MAX(时间窗口结束) as 最晚时间
        FROM v_dict_prj_yisi_chaifen 
        GROUP BY 客户名称
        ORDER BY 项目总数 DESC, 平均IT收入 DESC
        """
        
        df = pd.read_sql(query, conn)
        
        print(f"{'客户名称':<25} {'项目数':<6} {'拆分记录':<8} {'平均收入':<10} {'风险等级':<10} {'时间跨度'}")
        print("-" * 90)
        
        for _, row in df.iterrows():
            customer_name = row['客户名称'][:22] + "..." if len(row['客户名称']) > 25 else row['客户名称']
            time_span = f"{row['最早时间']}~{row['最晚时间']}"
            
            print(f"{customer_name:<25} {row['项目总数']:<6} {row['疑似拆分记录数']:<8} "
                  f"{row['平均IT收入']:<10.2f} {row['风险等级分布']:<10} {time_span}")
        
        conn.close()
        
    except Exception as e:
        print(f"[错误] 客户分析失败: {e}")

def show_project_code_analysis():
    """项目编码分析"""
    try:
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        print("\n=== 项目编码分析 ===")
        
        # 检查重复的项目编码
        cursor.execute("""
            SELECT 
                项目编码,
                COUNT(*) as 出现次数,
                GROUP_CONCAT(DISTINCT 客户名称) as 涉及客户,
                GROUP_CONCAT(DISTINCT 原记录ID) as 原记录ID列表
            FROM v_dict_prj_yisi_chaifen 
            GROUP BY 项目编码
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
        """)
        
        duplicates = cursor.fetchall()
        if duplicates:
            print("发现重复的项目编码:")
            for row in duplicates:
                print(f"  {row[0]} - 出现{row[1]}次 - 客户: {row[2]} - 原记录: {row[3]}")
        else:
            print("没有发现重复的项目编码")
        
        # 项目编码格式分析
        cursor.execute("""
            SELECT 
                SUBSTRING(项目编码, 1, 10) as 编码前缀,
                COUNT(*) as 数量,
                MIN(时间窗口开始) as 最早时间,
                MAX(时间窗口结束) as 最晚时间
            FROM v_dict_prj_yisi_chaifen 
            GROUP BY SUBSTRING(项目编码, 1, 10)
            ORDER BY 数量 DESC
        """)
        
        print("\n项目编码前缀分布:")
        prefixes = cursor.fetchall()
        for row in prefixes:
            print(f"  {row[0]} - {row[1]}个项目 - 时间跨度: {row[2]} ~ {row[3]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"[错误] 项目编码分析失败: {e}")

def show_risk_analysis():
    """风险等级分析"""
    try:
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        
        print("\n=== 风险等级分析 ===")
        
        query = """
        SELECT 
            风险等级,
            COUNT(*) as 项目数量,
            COUNT(DISTINCT 客户名称) as 涉及客户数,
            AVG(IT收入累加值) as 平均IT收入,
            SUM(IT收入累加值) as 总IT收入,
            MIN(IT收入累加值) as 最小IT收入,
            MAX(IT收入累加值) as 最大IT收入
        FROM v_dict_prj_yisi_chaifen 
        GROUP BY 风险等级
        ORDER BY 
            CASE 风险等级 
                WHEN '高' THEN 1 
                WHEN '中' THEN 2 
                WHEN '低' THEN 3 
                ELSE 4 
            END
        """
        
        df = pd.read_sql(query, conn)
        
        print(f"{'风险等级':<8} {'项目数':<8} {'客户数':<8} {'平均收入':<12} {'总收入':<12} {'收入范围'}")
        print("-" * 70)
        
        for _, row in df.iterrows():
            income_range = f"{row['最小IT收入']:.2f}~{row['最大IT收入']:.2f}"
            print(f"{row['风险等级']:<8} {row['项目数量']:<8} {row['涉及客户数']:<8} "
                  f"{row['平均IT收入']:<12.2f} {row['总IT收入']:<12.2f} {income_range}")
        
        conn.close()
        
    except Exception as e:
        print(f"[错误] 风险分析失败: {e}")

def export_to_excel():
    """导出视图数据到Excel"""
    try:
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        
        print("\n=== 导出数据到Excel ===")
        
        # 导出完整视图数据
        query = """
        SELECT 
            原记录ID,
            客户名称,
            项目编码,
            项目名称,
            项目序号,
            原记录项目总数,
            时间窗口开始,
            时间窗口结束,
            IT收入累加值,
            单项目最大IT收入,
            合同含税金额累加值,
            风险等级,
            检测时间
        FROM v_dict_prj_yisi_chaifen 
        ORDER BY 原记录ID, 项目序号
        """
        
        df = pd.read_sql(query, conn)
        
        # 生成文件名
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"疑似拆分项目明细_拆分视图_{timestamp}.xlsx"
        
        # 导出到Excel
        df.to_excel(filename, index=False, sheet_name='拆分项目明细')
        
        print(f"数据已导出到: {filename}")
        print(f"导出记录数: {len(df)}")
        
        conn.close()
        
    except Exception as e:
        print(f"[错误] 导出Excel失败: {e}")

if __name__ == "__main__":
    print("视图 v_dict_prj_yisi_chaifen 查询分析工具")
    print("=" * 60)
    
    # 显示视图概览
    show_view_overview()
    
    # 显示详细记录
    show_detailed_records()
    
    # 客户分析
    show_customer_analysis()
    
    # 项目编码分析
    show_project_code_analysis()
    
    # 风险分析
    show_risk_analysis()
    
    # 导出Excel
    export_to_excel()
    
    print("\n[完成] 视图分析完成")
