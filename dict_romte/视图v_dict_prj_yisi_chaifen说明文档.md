# 视图 v_dict_prj_yisi_chaifen 说明文档

## 📋 概述

视图 `v_dict_prj_yisi_chaifen` 是对 `dict_prj_yisi_chaifen` 表的项目编码列表进行拆分的视图，将原本以逗号分隔的项目编码列表拆分为单个项目记录，便于进行项目级别的分析和查询。

## 🎯 功能特点

### 1. 数据拆分
- **原始数据**: 每条记录包含多个项目编码（逗号分隔）
- **拆分结果**: 每个项目编码对应一条独立记录
- **数据完整性**: 保留原记录的所有字段信息

### 2. 拆分效果
- **原表记录数**: 8条
- **拆分后记录数**: 18条
- **涉及客户数**: 7个
- **唯一项目编码数**: 17个（发现1个重复项目编码）

## 📊 数据结构

### 主要字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 原记录ID | INT | 对应dict_prj_yisi_chaifen表的id |
| 客户名称 | VARCHAR(500) | 客户名称 |
| 项目编码 | TEXT | 拆分后的单个项目编码 |
| 项目名称 | TEXT | 对应的项目名称 |
| 项目序号 | INT | 该项目在原记录中的序号（1,2,3...） |
| 原记录项目总数 | INT | 原记录包含的项目总数 |
| IT收入累加值 | DECIMAL(15,2) | IT收入累加值（万元） |
| 风险等级 | VARCHAR(20) | 风险等级（高/中/低） |
| 时间窗口开始 | DATE | 时间窗口开始日期 |
| 时间窗口结束 | DATE | 时间窗口结束日期 |

## 📈 数据分析结果

### 1. 客户项目分布
```
客户名称                      项目数    拆分记录     平均收入       风险等级
嘉和众拓科技有限公司                4      2        477.88     低
中山市公安局横栏分局                3      1        829.76     中
中粤绿能（广东）建设工程有限公司          3      1        705.19     中
中国建筑一局（集团）有限公司            2      1        673.04     中
中山市公安局交通警察支队              2      1        547.93     低
湖北赢在起点企业管理有限公司            2      1        479.38     低
广东乾佰建设工程有限公司              2      1        475.87     低
```

### 2. 风险等级分布
```
风险等级     项目数      客户数      平均收入         总收入
中        8        3        743.87       5950.93
低        10       4        491.79       4917.86
```

### 3. 发现的问题
- **重复项目编码**: `CMGDZSICT20240507045` 在同一客户（中山市公安局横栏分局）的记录中出现2次
- **项目编码格式**: 所有项目编码都以 `CMGDZSICT2` 开头，符合统一的编码规范

## 🔍 使用示例

### 1. 查看所有拆分后的记录
```sql
SELECT * FROM v_dict_prj_yisi_chaifen
ORDER BY 原记录ID, 项目序号;
```

### 2. 按客户查看项目
```sql
SELECT 客户名称, 项目编码, 项目名称, IT收入累加值 
FROM v_dict_prj_yisi_chaifen 
WHERE 客户名称 LIKE '%中山%'
ORDER BY IT收入累加值 DESC;
```

### 3. 统计每个客户的项目数量
```sql
SELECT 
    客户名称, 
    COUNT(*) as 项目数量,
    AVG(IT收入累加值) as 平均收入
FROM v_dict_prj_yisi_chaifen 
GROUP BY 客户名称
ORDER BY 项目数量 DESC;
```

### 4. 查找重复的项目编码
```sql
SELECT 
    项目编码,
    COUNT(*) as 出现次数,
    GROUP_CONCAT(DISTINCT 客户名称) as 涉及客户
FROM v_dict_prj_yisi_chaifen 
GROUP BY 项目编码
HAVING COUNT(*) > 1;
```

### 5. 风险等级分析
```sql
SELECT 
    风险等级,
    COUNT(*) as 项目数量,
    AVG(IT收入累加值) as 平均收入,
    SUM(IT收入累加值) as 总收入
FROM v_dict_prj_yisi_chaifen 
GROUP BY 风险等级
ORDER BY 
    CASE 风险等级 
        WHEN '高' THEN 1 
        WHEN '中' THEN 2 
        WHEN '低' THEN 3 
    END;
```

## 🛠️ 技术实现

### 视图创建SQL
```sql
CREATE VIEW v_dict_prj_yisi_chaifen AS
SELECT 
    d.id AS 原记录ID,
    d.客户名称,
    d.时间窗口开始,
    d.时间窗口结束,
    d.涉及项目数量 AS 原记录项目总数,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目编码列表, ',', numbers.n), ',', -1)) AS 项目编码,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目名称列表, ',', numbers.n), ',', -1)) AS 项目名称,
    numbers.n AS 项目序号,
    d.IT收入累加值,
    d.风险等级,
    -- 其他字段...
FROM dict_prj_yisi_chaifen d
CROSS JOIN (
    SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
    SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL 
    SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
) numbers
WHERE 
    numbers.n <= (
        CHAR_LENGTH(d.项目编码列表) - CHAR_LENGTH(REPLACE(d.项目编码列表, ',', '')) + 1
    )
    AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目编码列表, ',', numbers.n), ',', -1)) != ''
ORDER BY d.id, numbers.n;
```

### 核心技术点
1. **CROSS JOIN**: 使用数字序列表与原表进行笛卡尔积
2. **SUBSTRING_INDEX**: 用于按逗号分隔符拆分字符串
3. **CHAR_LENGTH计算**: 动态计算每条记录的项目数量
4. **TRIM函数**: 清理拆分后字符串的空格

## 📁 相关文件

1. **create_view_dict_prj_yisi_chaifen.sql** - 视图创建SQL脚本
2. **execute_create_view.py** - 执行视图创建的Python脚本
3. **query_view_dict_prj_yisi_chaifen.py** - 视图查询分析工具
4. **analyze_project_codes.py** - 原始数据分析工具

## 🎯 应用场景

### 1. 项目级别分析
- 单个项目的收入分析
- 项目时间分布分析
- 项目编码规范性检查

### 2. 客户关系分析
- 客户项目数量统计
- 客户风险等级分布
- 客户收入贡献分析

### 3. 数据质量检查
- 重复项目编码检测
- 项目名称与编码一致性检查
- 数据完整性验证

### 4. 报表生成
- 项目明细报表
- 客户项目分布报表
- 风险等级统计报表

## 📊 导出功能

视图支持导出为Excel格式，包含以下信息：
- 完整的项目明细数据
- 按原记录ID和项目序号排序
- 包含所有关键字段信息

导出文件命名格式：`疑似拆分项目明细_拆分视图_YYYYMMDD_HHMMSS.xlsx`

## 🔄 维护说明

### 数据更新
- 视图会自动反映原表 `dict_prj_yisi_chaifen` 的数据变化
- 无需手动维护视图数据

### 性能优化
- 视图基于原表数据实时计算
- 对于大数据量场景，建议考虑创建物化视图
- 可在原表的关键字段上创建索引以提升查询性能

### 扩展性
- 支持最多10个项目的拆分（可通过修改numbers序列扩展）
- 可根据需要添加更多分析字段
- 支持与其他表进行关联查询

---

**创建时间**: 2025-07-31  
**版本**: 1.0  
**维护人**: 系统管理员
