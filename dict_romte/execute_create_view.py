#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
execute_create_view.py - 执行创建视图v_dict_prj_yisi_chaifen的SQL脚本
"""

import pymysql
from config import get_db_config

def execute_create_view():
    """执行创建视图的SQL脚本"""
    try:
        # 连接数据库
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        print("[信息] 正在创建视图 v_dict_prj_yisi_chaifen...")
        
        # 读取SQL文件
        with open('create_view_dict_prj_yisi_chaifen.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 直接执行关键的SQL语句
        print("[信息] 删除已存在的视图...")
        try:
            cursor.execute("DROP VIEW IF EXISTS v_dict_prj_yisi_chaifen")
            conn.commit()
            print("[成功] 已删除旧视图")
        except Exception as e:
            print(f"[警告] 删除旧视图时出现问题: {e}")

        print("[信息] 创建新视图...")
        create_view_sql = """
        CREATE VIEW v_dict_prj_yisi_chaifen AS
        SELECT
            -- 原记录标识
            d.id AS 原记录ID,

            -- 基本信息
            d.客户名称,
            d.时间窗口开始,
            d.时间窗口结束,
            d.涉及项目数量 AS 原记录项目总数,

            -- 拆分后的项目信息
            TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目编码列表, ',', numbers.n), ',', -1)) AS 项目编码,
            TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目名称列表, ',', numbers.n), ',', -1)) AS 项目名称,
            TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.收入侧合同编码列表, ',', numbers.n), ',', -1)) AS 收入侧合同编码,

            -- 项目序号信息
            numbers.n AS 项目序号,

            -- 财务信息（保持原值）
            d.IT收入累加值,
            d.单项目最大IT收入,
            d.合同含税金额累加值,

            -- 相似度分析信息
            d.CT内容平均相似度,
            d.IT内容平均相似度,
            d.CT内容最高相似度,
            d.IT内容最高相似度,
            d.综合相似度评分,
            d.相似度分析详情,
            d.相似度判断依据,

            -- 分布信息
            d.项目阶段分布,
            d.所属行业分布,
            d.归属区县分布,
            d.项目经理列表,

            -- 其他信息
            d.风险等级,
            d.检测时间,
            d.数据来源,
            d.备注

        FROM dict_prj_yisi_chaifen d
        CROSS JOIN (
            SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL
            SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL
            SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
        ) numbers
        WHERE
            -- 确保序号不超过实际项目数量
            numbers.n <= (
                CHAR_LENGTH(d.项目编码列表) - CHAR_LENGTH(REPLACE(d.项目编码列表, ',', '')) + 1
            )
            -- 确保拆分出的项目编码不为空
            AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目编码列表, ',', numbers.n), ',', -1)) != ''

        ORDER BY d.id, numbers.n
        """

        cursor.execute(create_view_sql)
        conn.commit()
        
        print("[成功] 视图 v_dict_prj_yisi_chaifen 创建完成")
        
        # 验证视图创建结果
        print("\n[信息] 验证视图创建结果...")
        cursor.execute("SELECT COUNT(*) FROM v_dict_prj_yisi_chaifen")
        view_count = cursor.fetchone()[0]
        print(f"视图记录数: {view_count}")
        
        # 查看前5条记录
        print("\n[信息] 查看前5条拆分记录:")
        cursor.execute("""
            SELECT 
                原记录ID,
                客户名称,
                项目编码,
                项目名称,
                项目序号,
                原记录项目总数
            FROM v_dict_prj_yisi_chaifen 
            ORDER BY 原记录ID, 项目序号 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        for row in results:
            print(f"  原记录ID:{row[0]} | 客户:{row[1][:15]}... | 项目编码:{row[2]} | 序号:{row[4]}/{row[5]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"[错误] 创建视图失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_view_queries():
    """测试视图查询功能"""
    try:
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        print("\n=== 视图查询测试 ===")
        
        # 测试1：统计每个客户的项目数量
        print("\n1. 每个客户的项目数量统计:")
        cursor.execute("""
            SELECT 
                客户名称,
                COUNT(*) as 拆分后项目数,
                COUNT(DISTINCT 原记录ID) as 原记录数,
                AVG(原记录项目总数) as 平均每记录项目数
            FROM v_dict_prj_yisi_chaifen 
            GROUP BY 客户名称
            ORDER BY 拆分后项目数 DESC
        """)
        
        results = cursor.fetchall()
        for row in results:
            print(f"  {row[0][:20]}... | 拆分项目:{row[1]} | 原记录:{row[2]} | 平均:{row[3]:.1f}")
        
        # 测试2：查看特定客户的详细信息
        print("\n2. 中山市公安局相关项目详情:")
        cursor.execute("""
            SELECT 
                项目编码,
                项目名称,
                IT收入累加值,
                风险等级
            FROM v_dict_prj_yisi_chaifen 
            WHERE 客户名称 LIKE '%中山市公安局%'
            ORDER BY 原记录ID, 项目序号
        """)
        
        results = cursor.fetchall()
        for row in results:
            print(f"  {row[0]} | {row[1][:30]}... | 收入:{row[2]} | 风险:{row[3]}")
        
        # 测试3：验证数据完整性
        print("\n3. 数据完整性验证:")
        cursor.execute("""
            SELECT 
                COUNT(*) as 视图总记录数,
                COUNT(DISTINCT 原记录ID) as 原表记录数,
                SUM(原记录项目总数) / COUNT(DISTINCT 原记录ID) as 平均项目数
            FROM v_dict_prj_yisi_chaifen
        """)
        
        result = cursor.fetchone()
        print(f"  视图总记录数: {result[0]}")
        print(f"  原表记录数: {result[1]}")
        print(f"  平均项目数: {result[2]:.2f}")
        
        cursor.close()
        conn.close()
        
        print("\n[成功] 视图查询测试完成")
        
    except Exception as e:
        print(f"[错误] 视图查询测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("创建视图 v_dict_prj_yisi_chaifen")
    print("=" * 50)
    
    # 创建视图
    if execute_create_view():
        # 测试视图功能
        test_view_queries()
    else:
        print("[失败] 视图创建失败，跳过测试")
